import { Channel } from '../utils/types';
import MetaImage from '../../../../pages/social-listening/images/integrations/meta_ads_logo.png';
import GoogleImage from '../../../../assets/icons/kpi/gads.png';
import ShopifyImage from '../../../../assets/icons/kpi/shopify.png';
import AmazonImage from '../../../../assets/icons/kpi/amazon-ads.png';
import AmazonSellerImage from '../../../../assets/icons/kpi/amazon-seller.png';
import googleAnalyticsImage from '../../../../pages/social-listening/images/integrations/googleAnalytics.png';
export const CHANNELS: Channel[] = [
   { id: 'facebookads', name: 'Meta Ads', icon: MetaImage },
   { id: 'googleads', name: 'Google Ads', icon: GoogleImage },
   { id: 'store', name: 'Shopify', icon: ShopifyImage },
   { id: 'web', name: 'Web Analytics', icon: googleAnalyticsImage },
   {
      id: 'amazon_selling_partner',
      name: 'Amazon Selling Partner',
      icon: AmazonSellerImage,
   },
   { id: 'amazon_ads', name: 'Amazon Ads', icon: AmazonImage },
];
export const CHANNEL_MAP: Record<string, Channel> = Object.fromEntries(
   CHANNELS.map((ch) => [ch.id, ch]),
);
export const headersByChannel: Record<string, string[]> = {
   googleads: ['Month', 'Spend', 'Conversions', 'CPC Target', 'CTR Target'],

   facebookads: ['Month', 'Impressions', 'Facebook Ads Spent', 'CPM', 'CTR'],
   /*ads spent,roas,ctr,cpp,

   ind wise-> leads ,cpl*/

   store: ['Month', 'Revenue', 'Orders', 'AOV', 'Conversion Rate'],

   amazon_ads: ['Month', 'Ad Spend', 'Total Sales', 'ACOS', 'ROAS'],

   amazon_selling_partner: [
      'Month',
      'Total Orders',
      'Gross Sales',
      'Refund Rate',
      'Buy Box %',
   ],

   web: [
      'Month',
      'Sessions',
      'Users',
      'Bounce Rate',
      'Average Session Duration',
   ],
};

"use client";
import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarI<PERSON>, ChevronLeft, ChevronRight } from "lucide-react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/index";

interface MonthPickerProps {
  value: string;
  onChange: (val: string) => void;
}

const months = [
  "Jan", "Feb", "Mar", "Apr",
  "May", "Jun", "Jul", "Aug",
  "Sep", "Oct", "Nov", "Dec"
];

export default function MonthPicker({ value, onChange }: MonthPickerProps) {
  const [open, setOpen] = React.useState(false);

  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  const [year, setYear] = React.useState<number>(currentYear);
  const [month, setMonth] = React.useState<number | null>(null); // null if not selected yet

  // ✅ update state when a value prop comes in
  React.useEffect(() => {
    if (value) {
      const d = new Date(value);
      setYear(d.getFullYear());
      setMonth(d.getMonth());
    } else {
      setMonth(null);
      setYear(currentYear);
    }
  }, [value]);

  const handleMonthSelect = (index: number) => {
    if (year === currentYear && index < currentMonth) return;
    const selectedDate = new Date(year, index);
    setMonth(index);
    onChange(format(selectedDate, "yyyy-MM"));
    setOpen(false);
  };

  const handlePrevYear = () => {
    if (year > currentYear) setYear((y) => y - 1);
  };

  const handleNextYear = () => {
    setYear((y) => y + 1);
  };

  return (
    <PopoverPrimitive.Root open={open} onOpenChange={setOpen}>
      <PopoverPrimitive.Trigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full h-8 text-sm justify-start text-left font-normal relative",
            !month && "text-gray-400"
          )}
          type="button"
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {month !== null ? `${months[month]} ${year}` : "Select Month"}
        </Button>
      </PopoverPrimitive.Trigger>

      {/* ✅ FIXED: Popover positions directly below the field */}
      <PopoverPrimitive.Portal>
        <PopoverPrimitive.Content
          side="bottom"
          align="start"
          sideOffset={4}
          collisionPadding={8}
          className={cn(
            "z-[9999] w-[260px] p-4 bg-white dark:bg-neutral-900 shadow-xl border border-gray-200 dark:border-neutral-800 rounded-md"
          )}
        >
          {/* Year navigation */}
          <div className="flex justify-between items-center mb-3">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 disabled:opacity-40"
              onClick={handlePrevYear}
              disabled={year <= currentYear}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="font-medium">{year}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleNextYear}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Month grid */}
          <div className="grid grid-cols-4 gap-2">
            {months.map((m, i) => {
              const isDisabled = year === currentYear && i < currentMonth;
              const isActive = month === i && year === new Date(value).getFullYear();
              return (
                <button
                  key={m}
                  onClick={() => handleMonthSelect(i)}
                  disabled={isDisabled}
                  className={cn(
                    "py-2 text-sm rounded-md transition-colors",
                    isDisabled
                      ? "text-gray-400 cursor-not-allowed opacity-60"
                      : "hover:bg-gray-100 dark:hover:bg-neutral-800 text-gray-700 dark:text-gray-200",
                    isActive
                      ? "bg-purple-500 text-white font-semibold"
                      : ""
                  )}
                >
                  {m}
                </button>
              );
            })}
          </div>
        </PopoverPrimitive.Content>
      </PopoverPrimitive.Portal>
    </PopoverPrimitive.Root>
  );
}
